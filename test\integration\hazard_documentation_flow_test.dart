import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/usecases/create_hazard.dart';
import 'package:safestride/domain/usecases/update_hazard.dart';
import 'package:safestride/domain/repositories/hazard_repository.dart';
import 'package:safestride/data/repositories/hazard_repository_impl.dart';
import 'package:safestride/data/datasources/local/hazard_local_datasource.dart';
import 'package:safestride/services/camera/camera_service.dart';
import 'package:safestride/services/location/location_service.dart';
import 'package:safestride/services/voice/voice_input_service.dart';
import 'package:safestride/presentation/providers/hazard_provider.dart';
import 'package:safestride/presentation/screens/hazard/hazard_documentation_screen.dart';
import 'package:safestride/presentation/screens/walkabout/walkabout_detail_screen.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Hazard Documentation Integration Tests', () {
    late HazardProvider hazardProvider;
    late Walkabout testWalkabout;

    setUp(() {
      // Create test walkabout
      testWalkabout = Walkabout(
        id: 'test_walkabout_integration',
        userId: 'test_user',
        title: 'Integration Test Walkabout',
        description: 'Test walkabout for integration testing',
        status: WalkaboutStatus.inProgress,
        location: const GeoPoint(latitude: 37.7749, longitude: -122.4194),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.local,
      );

      // Setup providers with mock implementations for testing
      // Note: In a real integration test, you might use actual implementations
      // or test-specific implementations that don't require real hardware
    });

    Widget createTestApp() {
      return MaterialApp(
        home: ChangeNotifierProvider<HazardProvider>(
          create: (_) => hazardProvider,
          child: WalkaboutDetailScreen(walkabout: testWalkabout),
        ),
      );
    }

    testWidgets('Complete hazard documentation flow', (tester) async {
      // This test demonstrates the complete flow from walkabout detail
      // to hazard documentation and back
      
      // Arrange
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Act & Assert: Navigate to hazard documentation
      expect(find.text('Integration Test Walkabout'), findsOneWidget);
      
      // Tap the floating action button to document a new hazard
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      // Verify we're on the hazard documentation screen
      expect(find.text('Document Hazard'), findsOneWidget);

      // Fill in hazard details
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Hazard Title *'),
        'Test Integration Hazard',
      );
      await tester.pumpAndSettle();

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Description'),
        'This is a test hazard for integration testing',
      );
      await tester.pumpAndSettle();

      // Select severity level
      await tester.tap(find.text('High'));
      await tester.pumpAndSettle();

      // Select category
      await tester.tap(find.byType(DropdownButtonFormField<HazardCategory>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Electrical').last);
      await tester.pumpAndSettle();

      // Add notes
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Additional Notes'),
        'Integration test notes',
      );
      await tester.pumpAndSettle();

      // Save the hazard
      await tester.tap(find.text('Save Hazard'));
      await tester.pumpAndSettle();

      // Verify we're back on the walkabout detail screen
      expect(find.text('Integration Test Walkabout'), findsOneWidget);
      
      // Verify the hazard appears in the hazards tab
      await tester.tap(find.text('Hazards'));
      await tester.pumpAndSettle();
      
      expect(find.text('Test Integration Hazard'), findsOneWidget);
    });

    testWidgets('Hazard editing flow', (tester) async {
      // This test verifies that hazards can be edited after creation
      
      // Arrange: Create a hazard first
      final existingHazard = Hazard(
        id: 'test_hazard_edit',
        walkaboutId: testWalkabout.id,
        title: 'Original Hazard Title',
        description: 'Original description',
        severity: HazardSeverity.low,
        category: HazardCategory.other,
        photos: [],
        notes: 'Original notes',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.local,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
              existingHazard: existingHazard,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Act & Assert: Verify editing mode
      expect(find.text('Edit Hazard'), findsOneWidget);
      expect(find.text('Update'), findsOneWidget);

      // Verify form is pre-populated
      expect(find.text('Original Hazard Title'), findsOneWidget);
      expect(find.text('Original description'), findsOneWidget);
      expect(find.text('Original notes'), findsOneWidget);

      // Edit the title
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Hazard Title *'),
        'Updated Hazard Title',
      );
      await tester.pumpAndSettle();

      // Change severity
      await tester.tap(find.text('Critical'));
      await tester.pumpAndSettle();

      // Update the hazard
      await tester.tap(find.text('Update Hazard'));
      await tester.pumpAndSettle();

      // Verify navigation back (in real app, this would navigate back)
      // In this test, we just verify the update button was tapped
    });

    testWidgets('Photo capture simulation', (tester) async {
      // This test simulates the photo capture flow
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify initial state shows no photos
      expect(find.text('No photos added'), findsOneWidget);

      // Tap camera button to simulate photo capture
      await tester.tap(find.byIcon(Icons.camera_alt));
      await tester.pumpAndSettle();

      // In a real integration test with actual camera service,
      // we would verify that a photo was added to the selection
      // For this test, we just verify the button was tapped
    });

    testWidgets('Location tagging simulation', (tester) async {
      // This test simulates the location tagging flow
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Verify initial state shows no location
      expect(find.text('No location selected'), findsOneWidget);

      // Tap get current location button
      await tester.tap(find.text('Get Current'));
      await tester.pumpAndSettle();

      // In a real integration test with actual location service,
      // we would verify that location coordinates are displayed
      // For this test, we just verify the button was tapped
    });

    testWidgets('Voice input simulation', (tester) async {
      // This test simulates the voice input flow
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find the voice input button in the description field
      final micButton = find.byIcon(Icons.mic_none);
      expect(micButton, findsOneWidget);

      // Tap to start voice input
      await tester.tap(micButton);
      await tester.pumpAndSettle();

      // In a real integration test with actual voice service,
      // we would verify that voice input is activated and
      // recognized text appears in the interface
      // For this test, we just verify the button was tapped
    });

    testWidgets('Form validation integration', (tester) async {
      // This test verifies form validation works end-to-end
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Try to save without entering required fields
      await tester.tap(find.text('Save Hazard'));
      await tester.pump();

      // Verify validation error appears
      expect(find.text('Please enter a hazard title'), findsOneWidget);

      // Enter a title that's too long
      final longTitle = 'a' * 101;
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Hazard Title *'),
        longTitle,
      );
      await tester.pump();

      await tester.tap(find.text('Save Hazard'));
      await tester.pump();

      // Verify length validation error appears
      expect(find.text('Title cannot exceed 100 characters'), findsOneWidget);

      // Enter valid title
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Hazard Title *'),
        'Valid Hazard Title',
      );
      await tester.pump();

      // Now save should work (assuming other validations pass)
      await tester.tap(find.text('Save Hazard'));
      await tester.pumpAndSettle();
    });

    testWidgets('Severity and category selection integration', (tester) async {
      // This test verifies severity and category selection works properly
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<HazardProvider>(
            create: (_) => hazardProvider,
            child: HazardDocumentationScreen(
              walkaboutId: testWalkabout.id,
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Test severity selection
      expect(find.text('Medium'), findsOneWidget); // Default selection

      await tester.tap(find.text('Critical'));
      await tester.pumpAndSettle();

      // Verify critical is now selected (would be highlighted in real UI)

      // Test category selection
      await tester.tap(find.byType(DropdownButtonFormField<HazardCategory>));
      await tester.pumpAndSettle();

      // Verify all categories are available
      for (final category in HazardCategory.values) {
        expect(find.text(category.displayName), findsAtLeastNWidgets(1));
      }

      // Select a specific category
      await tester.tap(find.text('Fire').last);
      await tester.pumpAndSettle();

      // Verify the dropdown closed and selection was made
      expect(find.byType(DropdownButtonFormField<HazardCategory>), findsOneWidget);
    });
  });
}
